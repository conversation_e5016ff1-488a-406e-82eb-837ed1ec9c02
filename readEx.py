import pandas as pd
import numpy as np
from pathlib import Path

# === 1. 读取 3 张 Sheet =========================================================
xls_path = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
                r"\安徽中小流域资料整理 - 给孟涵 - 20230903\月潭次模率定.xls")

# 注意：若换成 .xlsx，请将 engine 改为 "openpyxl"
summary_df = pd.read_excel(xls_path, sheet_name="summary", engine="xlrd")
data_df = pd.read_excel(xls_path, sheet_name="data", engine="xlrd")
evap_df = pd.read_excel(xls_path, sheet_name="evapd", engine="xlrd")


# === 2. 时间字段预处理 ==========================================================
def int2dt(series, unit="H"):
    fmt = "%Y%m%d%H" if unit == "H" else "%Y%m%d"
    return pd.to_datetime(series.astype(str).str.zfill(len(fmt) - 2), format=fmt)


# -- data sheet -----------------------------------------------------------------
data_df["ts"] = int2dt(data_df.iloc[:, 0], unit="H")  # 第一列整数 → 时间戳
data_df.set_index("ts", inplace=True)

# -- evap sheet -----------------------------------------------------------------
evap_df["date"] = int2dt(evap_df.iloc[:, 0], unit="D")  # 第一列整数 → 日期
evap_df.set_index("date", inplace=True)

# （可选）把原始整数日期列删掉，避免后面 columns[1] 越界：
evap_df.drop(columns=evap_df.columns[0], inplace=True)

# 将日蒸发量平铺为小时值
evap_hourly = (
    evap_df
    .resample("h")  # 'H' is deprecated and will be removed in a future version, please use 'h' instead.
    .ffill()  # 每 24 行相同数值；如需除以 24 再 .div(24)
    .rename(columns={evap_df.columns[0]: "evap"})
)

# === 3. 生成特征矩阵 ============================================================
flow_col = data_df.columns[1]  # 流量 (第 2 列)
rain_cols = data_df.columns[2:]  # 各站降雨 (第 3 列起)
feature_df = data_df[[flow_col, *rain_cols]].join(evap_hourly["evap"], how="left")

# === 4. 按场次切片存入字典 ======================================================
events = {}

for _, row in summary_df.iterrows():
    event_id = row.iloc[0]  # 场次编号/洪号
    start_time = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    end_time = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")

    seg = feature_df.loc[start_time:end_time]

    if seg.empty:  # 避免后续模型训练时 shape=0 的异常
        print(f"[警告] 场次 {event_id} 在 data 中无有效记录，已跳过。")
        continue

    events[event_id] = seg.to_numpy(dtype=np.float32)

print(f"✔ 解析完成：共 {len(events)} 场\n"
      f"  例：event {next(iter(events))} → shape {events[next(iter(events))].shape}")
