import pandas as pd
import numpy as np
from pathlib import Path

# === 1. 读取 3 张关键 Sheet ====================================================
xls_path = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\安徽中小流域资料整理 - 给孟涵 - 2023090355\月潭次模率定.xls")
# 👉 如果没安装 xlrd / openpyxl，请先:  pip install xlrd openpyxl

summary_df = pd.read_excel(xls_path, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(xls_path, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(xls_path, sheet_name="evapd",   engine="xlrd")

# === 2. 时间字段预处理 =========================================================
def int2dt(series, unit="h"):
    """
    将整型日期(时间)转换为 pd.Timestamp.
    unit="h" 适用于 data sheet   (如 2024080613  → 2024-08-06 13:00)
    unit="D" 适用于 evapd sheet (如 20240806    → 2024-08-06 00:00)
    """
    fmt  = "%Y%m%d%H" if unit == "h" else "%Y%m%d"
    return pd.to_datetime(series.astype(str), format=fmt)

data_df["ts"]   = int2dt(data_df.iloc[:, 0], unit="h")   # 第一列为日期时间整数
evap_df["date"] = int2dt(evap_df.iloc[:, 0], unit="D")   # 第一列为日期整数

data_df = data_df.set_index("ts")
evap_df = evap_df.set_index("date")

# 蒸发是“日值”→ 扩展为小时序列，方便后续按场次切片
evap_hourly = (
    evap_df
    .resample("H")
    .ffill()                    # 假设日蒸发量代表当天每小时相同，可按需改成/24
    .rename(columns={evap_df.columns[1]: "evap"})
)
# === 3. 计算“特征矩阵”  =========================================================
# data_df 第二列是总出口流量；后面若干列为各子站降雨；再往后若有 inflow 也可一起取
flow_col      = data_df.columns[1]                 # 流量
rain_cols     = data_df.columns[2:]                # 降雨(若后面有 inflow, 请手动分界)
feature_cols  = [flow_col] + list(rain_cols)       # 先放流量, 再按顺序放降雨
# 将蒸发列并到同一 DataFrame
merged_df = data_df[feature_cols].join(evap_hourly["evap"], how="left")

# === 4. 按场次切片并拼成二维数组 ==============================================
events = {}  # {洪号/序号: ndarray}

for _, row in summary_df.iterrows():
    event_id   = row["洪号"] if "洪号" in row else row["序号"]
    start_time = pd.to_datetime(str(int(row["洪水起始日期时间"])), format="%Y%m%d%H")
    end_time   = pd.to_datetime(str(int(row["洪水终止日期时间"])), format="%Y%m%d%H")

    # 取出该场次的所有行
    seg = merged_df.loc[start_time:end_time]

    # 若需要把“多个子站降雨”求和，可取消下一行注释
    # seg.loc[:, "rain_sum"] = seg[rain_cols].sum(axis=1)

    # 生成二维数组 (T × F)
    events[event_id] = seg.to_numpy(dtype=np.float32)

print(f"共解析到 {len(events)} 个场次，示例 event_id={next(iter(events))}，"
      f"shape={events[next(iter(events))].shape}")
