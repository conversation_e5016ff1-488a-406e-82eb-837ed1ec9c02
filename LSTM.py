# -*- coding: utf-8 -*-
"""
基于“月潭流域”预处理输出的 LSTM 训练 / 预测示例（PaddlePaddle 2.x）
--------------------------------------------------------------------
数据文件：
  ├─ train_lb6.npz      训练集（features=8, look_back=6）
  ├─ test_lb6.npz       测试集
  └─ minmax_scaler.pkl  归一化模型（推理时可反归一化）

模型结构：
  1. 单层 LSTM(hidden_size=64)
  2. 全连接回归头 → 1 个连续值（下一步流量）
"""

import os
import pickle
import numpy as np
import paddle
import paddle.nn as nn
from paddle.io import Dataset, DataLoader

# ========= 1. 数据文件完整路径 ================================================ #
TRAIN_NPZ = r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\预处理输出\train_look6.npz"
TEST_NPZ  = r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\预处理输出\test_look6.npz"
SCALER_PKL= r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\预处理输出\minmax_scaler.pkl"

# ========= 2. 超参数 ========================================================== #
BATCH_SIZE   = 64
EPOCHS       = 50
LR           = 1e-3
HIDDEN_SIZE  = 64       # LSTM 隐单元数
NUM_LAYERS   = 1        # LSTM 层数
LOOK_BACK    = 6        # 每条输入序列长度（小时数）
INPUT_DIM    = 8        # 每小时特征数（流量 + 6站降雨 + 蒸发 = 8）
TARGET_IDX   = 0        # y 的列下标（0 = 流量）

SEED = 42
paddle.seed(SEED)
np.random.seed(SEED)

# ========= 3. 自定义数据集 ==================================================== #
class FloodSeqDataset(Dataset):
    def __init__(self, npz_path):
        npz = np.load(npz_path)
        self.X = npz["X"].astype("float32")   # shape:(N, LOOK_BACK, INPUT_DIM)
        self.y = npz["y"].astype("float32")   # shape:(N,)
    def __len__(self):
        return self.X.shape[0]
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

train_ds = FloodSeqDataset(TRAIN_NPZ)
test_ds  = FloodSeqDataset(TEST_NPZ)

train_loader = DataLoader(train_ds, batch_size=BATCH_SIZE, shuffle=True,  drop_last=False)
test_loader  = DataLoader(test_ds,  batch_size=BATCH_SIZE, shuffle=False, drop_last=False)

# ========= 4. 模型定义 ======================================================== #
class LSTMRegressor(nn.Layer):
    def __init__(self, input_dim, hidden_size, num_layers, look_back):
        super().__init__()
        self.lstm = nn.LSTM(
            input_size = input_dim,
            hidden_size= hidden_size,
            num_layers = num_layers,
            direction  = "forward"
        )
        # LSTM 输出 shape:(seq_len, batch, hidden_size)
        self.fc = nn.Linear(hidden_size, 1)

    def forward(self, x):
        # x shape:(batch, seq_len, input_dim) ——> Paddle 需要 (seq_len, batch, input_dim)
        x = paddle.transpose(x, perm=[1, 0, 2])
        out, _ = self.lstm(x)            # 取最后一个时间步
        last_out = out[-1]               # shape:(batch, hidden)
        y_hat = self.fc(last_out)        # shape:(batch, 1)
        return y_hat.squeeze(-1)

model = LSTMRegressor(INPUT_DIM, HIDDEN_SIZE, NUM_LAYERS, LOOK_BACK)
criterion = nn.MSELoss()
optimizer = paddle.optimizer.Adam(learning_rate=LR, parameters=model.parameters())

# ========= 5. 训练 & 验证循环 ================================================= #
def evaluate(loader):
    model.eval()
    losses = []
    with paddle.no_grad():
        for X, y in loader:
            pred = model(X)
            loss = criterion(pred, y)
            losses.append(loss.numpy()[0])
    model.train()
    return float(np.mean(losses))

best_test = float("inf")
for epoch in range(1, EPOCHS + 1):
    for X, y in train_loader:
        pred = model(X)
        loss = criterion(pred, y)
        loss.backward()
        optimizer.step()
        optimizer.clear_grad()
    if epoch % 5 == 0 or epoch == 1:
        train_mse = evaluate(train_loader)
        test_mse  = evaluate(test_loader)
        if test_mse < best_test:
            best_test = test_mse
            paddle.save(model.state_dict(),
                        os.path.join(os.path.dirname(TRAIN_NPZ), "best_lstm.pdparams"))
        print(f"[{epoch:3d}/{EPOCHS}]  train_MSE={train_mse:.4f}  test_MSE={test_mse:.4f}  (best={best_test:.4f})")

print("\n✅ 训练完成，最优模型已保存为：",
      os.path.join(os.path.dirname(TRAIN_NPZ), "best_lstm.pdparams"))

# ========= 6. 推理示例（反归一化恢复流量值） ================================= #
with open(SCALER_PKL, "rb") as f:
    scaler = pickle.load(f)          # sklearn MinMaxScaler

def inverse_transform_flow(norm_val):
    """将归一化后的 ŷ 反归一化回原始流量单位"""
    dummy = np.zeros((1, INPUT_DIM), dtype=np.float32)
    dummy[0, TARGET_IDX] = norm_val
    return scaler.inverse_transform(dummy)[0, TARGET_IDX]

sample_X, sample_y = test_ds[0]                           # 第 1 条测试样本
model.set_state_dict(paddle.load(os.path.join(os.path.dirname(TRAIN_NPZ), "best_lstm.pdparams")))
model.eval()
with paddle.no_grad():
    pred_norm = model(paddle.to_tensor(sample_X[None, ...]))  # shape:(1,)
pred_val = inverse_transform_flow(pred_norm.numpy()[0])
true_val = inverse_transform_flow(sample_y)

print(f"\n[推理示例]  真实流量 = {true_val:.2f}  |  预测流量 = {pred_val:.2f}")
