# -*- coding: utf-8 -*-
"""
LSTM 结果可视化：逐场次绘制
------------------------------------------------------------
读取：
  ▸ 预测输出（模型 + 数据集）
  ▸ 原始 Excel   —— 为了拿到“实测流量”按场次切片
生成：
  ▸ hydrographs_all_events.pdf  —— 每页一场，实测 vs. 模拟
"""

import os, pickle, math
from pathlib import Path

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from tqdm import tqdm

import paddle
import paddle.nn as nn


# ========= ★ 路径（保持和前面一致即可） ========================================
ROOT_DIR = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903")
DATA_DIR = ROOT_DIR / "预处理输出"

TRAIN_NPZ = DATA_DIR / "train_look6.npz"     # 只是用来拿特征维度
TEST_NPZ  = DATA_DIR / "test_look6.npz"      # → 取得 (hist, exog) 做预测
SCALER_PKL = DATA_DIR / "minmax_scaler.pkl"
MODEL_WEIGHTS = DATA_DIR / "lstm_with_exog.pdparams"
OUT_PDF      = DATA_DIR / "hydrographs_all_events.pdf"

XLS_PATH = (ROOT_DIR /
            "安徽中小流域资料整理 - 给孟涵 - 20230903" /
            "月潭次模率定.xls")
# ==============================================================================


# -------------------- 1. 工具函数 ---------------------------------------------
def inv_flow(x_scaled: np.ndarray, data_min, data_range) -> np.ndarray:
    """把 0-1 归一化后的『流量』还原为原始单位 (m³/s)"""
    return x_scaled * data_range + data_min


def int2dt(series: pd.Series, unit: str = "H") -> pd.Series:
    fmt = "%Y%m%d%H" if unit.upper() == "H" else "%Y%m%d"
    return pd.to_datetime(series.astype(str).str.zfill(len(fmt) - 2), format=fmt)


# -------------------- 2. 载入 scaler（只取流量那一列参数） ---------------------
with open(SCALER_PKL, "rb") as f:
    scaler = pickle.load(f)

FLOW_IDX = 0                              # 流量列始终排第一
flow_min = scaler.data_min_[FLOW_IDX]
flow_rng = scaler.data_max_[FLOW_IDX] - scaler.data_min_[FLOW_IDX]

# -------------------- 3. 载入“实测”按场次切片 -------------------------------
summary_df = pd.read_excel(XLS_PATH, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(XLS_PATH, sheet_name="data",    engine="xlrd")

data_df["ts"] = int2dt(data_df.iloc[:, 0], unit="H")
data_df.set_index("ts", inplace=True)
flow_col = data_df.columns[1]             # 流量列名

obs_events = {}
for _, row in summary_df.iterrows():
    eid        = row.iloc[0]
    start_time = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    end_time   = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg = data_df.loc[start_time:end_time, flow_col]
    if seg.empty:
        continue
    obs_events[eid] = seg                 # pandas.Series


# -------------------- 4. 定义与训练时同款模型 ---------------------------------
class LSTMWithExog(nn.Layer):
    def __init__(self, input_dim: int, hidden_size: int,
                 num_layers: int, exog_dim: int = 2):
        super().__init__()
        self.lstm = nn.LSTM(input_size=input_dim,
                            hidden_size=hidden_size,
                            num_layers=num_layers,
                            direction="forward")
        self.fc = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, hist, exog):
        hist = paddle.transpose(hist, perm=[1, 0, 2])
        output, _ = self.lstm(hist)
        last_out = output[-1]
        x = paddle.concat([last_out, exog], axis=1)
        y_hat = self.fc(x)
        return y_hat.squeeze(1)


# -------------------- 5. 读取数据集做“批量推理” -------------------------------
test_npz = np.load(TEST_NPZ)
X_hist = test_npz["X_hist"].astype("float32")
X_exog = test_npz["X_exog"].astype("float32")

INPUT_DIM  = X_hist.shape[-1]     #  >>> 7
HIDDEN     = 128
NUM_LAYERS = 2

model = LSTMWithExog(INPUT_DIM, HIDDEN, NUM_LAYERS)
state_dict = paddle.load(MODEL_WEIGHTS)
model.set_state_dict(state_dict)
model.eval()
print(f"✔ 已加载权重：{MODEL_WEIGHTS}")

BATCH_SZ = 512
preds_scaled = []

for beg in tqdm(range(0, len(X_hist), BATCH_SZ), desc="Predict"):
    hist_blk = paddle.to_tensor(X_hist[beg:beg+BATCH_SZ])
    exog_blk = paddle.to_tensor(X_exog[beg:beg+BATCH_SZ])
    with paddle.no_grad():
        y_hat_blk = model(hist_blk, exog_blk).numpy()
    preds_scaled.append(y_hat_blk)

preds_scaled = np.concatenate(preds_scaled, axis=0)   # (N_test,)


# -------------------- 6. 把“预测流量”分配回各场次 -----------------------------
# ⚠️ 训练集 + 测试集在预处理时是“按场次时间顺序”拆分的，
#    所以可以用 summary_df 再按原顺序把预测结果切回去。
n_test = len(preds_scaled)
test_ratio = 0.2
split_idx = int(round((1 - test_ratio) * len(summary_df)))

test_events = summary_df.iloc[split_idx:]             # 仅测试集场次

sim_events = {}       # {eid: ndarray_of_pred_flow}
cursor = 0
for _, row in test_events.iterrows():
    eid        = row.iloc[0]
    start_time = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    end_time   = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    T = int((end_time - start_time).total_seconds() / 3600) + 1 - 6  # 可预测步数
    seg_pred = preds_scaled[cursor:cursor + T]
    cursor += T
    sim_events[eid] = seg_pred

assert cursor == n_test, "✘ 预测结果长度不匹配，请检查数据集切割方式"


# -------------------- 7. 反归一化 & 绘图 -------------------------------------
plt.rcParams["font.sans-serif"] = ["SimHei"]   # 支持中文
plt.rcParams["axes.unicode_minus"] = False

n_pages = len(sim_events)
n_rows, n_cols = 2, 1                           # 每页两行：实测 + 预测
pdf = plt.backends.backend_pdf.PdfPages(OUT_PDF)

for eid in tqdm(sim_events.keys(), desc="Plot"):
    obs = obs_events[eid].to_numpy()            # (T_obs,)
    sim = inv_flow(sim_events[eid], flow_min, flow_rng)

    t_obs = np.arange(obs.shape[0])
    t_sim = np.arange(6, 6 + sim.shape[0])      # 预测对应的是 t+1~T

    fig, ax = plt.subplots(figsize=(10, 4))
    ax.plot(t_obs, obs, label="实测", lw=1.5)
    ax.plot(t_sim, sim, label="模拟", lw=1.2)
    ax.set_title(f"洪水场次 {eid}")
    ax.set_xlabel("时间步 (h)")
    ax.set_ylabel("流量 (m³/s)")
    ax.legend()
    ax.grid(alpha=0.4)

    pdf.savefig(fig)
    plt.close(fig)

pdf.close()
print(f"\n✔ 全部场次过程线已保存：{OUT_PDF}")
