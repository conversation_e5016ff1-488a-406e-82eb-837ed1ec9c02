# -*- coding: utf-8 -*-
"""
月潭流域 LSTM 训练数据自动化预处理脚本
------------------------------------------------------------
1. 读取 summary / data / evapd 三张 Sheet
2. 线性插值补缺 + MinMax 归一化
3. 生成 (look_back 小时) → 下一步流量 的监督学习样本
4. 按场次顺序 8:2 划分训练/测试集
5. 保存压缩二进制 .npz 以及 scaler.pkl
"""

import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.preprocessing import MinMaxScaler
import pickle

# ========== ★ 用户根据实际情况修改 ↓↓↓ =========================================
xls_path    = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
                   r"\安徽中小流域资料整理 - 给孟涵 - 20230903\月潭次模率定.xls")
out_dir     = Path(r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903\预处理输出")
look_back   = 6       # LSTM 输入的历史步数
target_col  = 0       # 预测目标列索引（0=流量）
# ==============================================================================

out_dir.mkdir(parents=True, exist_ok=True)

# === 1. 读取 3 张 Sheet ========================================================
summary_df = pd.read_excel(xls_path, sheet_name="summary", engine="xlrd")
data_df    = pd.read_excel(xls_path, sheet_name="data",    engine="xlrd")
evap_df    = pd.read_excel(xls_path, sheet_name="evapd",   engine="xlrd")

# === 2. 时间字段预处理 =========================================================
def int2dt(s: pd.Series, unit: str="H") -> pd.DatetimeIndex:
    fmt = "%Y%m%d%H" if unit.upper()=="H" else "%Y%m%d"
    return pd.to_datetime(s.astype(str).str.zfill(len(fmt)-2), format=fmt)

# data
data_df["ts"] = int2dt(data_df.iloc[:,0], unit="H")
data_df.set_index("ts", inplace=True)
# evap
evap_df["date"] = int2dt(evap_df.iloc[:,0], unit="D")
evap_df.set_index("date", inplace=True)
evap_df.drop(columns=evap_df.columns[0], inplace=True)
evap_hourly = (
    evap_df
    .resample("h")
    .ffill()
    .rename(columns={evap_df.columns[0]: "evap"})
)

# === 3. 生成特征矩阵 ===========================================================
flow_col  = data_df.columns[1]
rain_cols = data_df.columns[2:]
feature_df = data_df[[flow_col, *rain_cols]].join(evap_hourly["evap"], how="left")

# === 4. 按场次切片存入 dict ====================================================
events = {}
for _, row in summary_df.iterrows():
    eid   = row.iloc[0]
    t0    = pd.to_datetime(str(int(row.iloc[1])), format="%Y%m%d%H")
    t1    = pd.to_datetime(str(int(row.iloc[3])), format="%Y%m%d%H")
    seg   = feature_df.loc[t0:t1]
    if seg.empty:
        print(f"[警告] 场次 {eid} 无记录，已跳过")
        continue
    events[eid] = seg.to_numpy(dtype=np.float32)
print(f"✔ 共解析到 {len(events)} 个场次")

# === 5. 缺测插值 ==============================================================
events_clean = {}
for eid, arr in events.items():
    df = pd.DataFrame(arr).interpolate().ffill().bfill()
    events_clean[eid] = df.to_numpy(np.float32)

# === 6. MinMax 归一化 =========================================================
all_data = np.concatenate(list(events_clean.values()), axis=0)
scaler   = MinMaxScaler((0,1)).fit(all_data)
events_scaled = {eid: scaler.transform(arr) for eid, arr in events_clean.items()}

# === 7+8. 滑动窗口 → 按场次顺序划分训练/测试 =====================================
train_Xs, train_ys = [], []
test_Xs,  test_ys  = [], []

# 先拿到按文件顺序的场次列表
eid_list = list(events_scaled.keys())
n_train = int(len(eid_list) * 0.8)
train_eids = set(eid_list[:n_train])
test_eids  = set(eid_list[n_train:])

for eid in eid_list:
    arr = events_scaled[eid]
    T, F = arr.shape
    if T <= look_back:
        print(f"[跳过] Event {eid} 长度<{look_back}")
        continue
    # 生成该场次的监督样本
    X_e, y_e = [], []
    for i in range(T - look_back):
        X_e.append(arr[i:i+look_back, :])
        y_e.append(arr[i+look_back, target_col])
    X_e = np.stack(X_e, axis=0)
    y_e = np.array(y_e, dtype=np.float32)
    # 分配到训练或测试
    if eid in train_eids:
        train_Xs.append(X_e)
        train_ys.append(y_e)
    else:
        test_Xs.append(X_e)
        test_ys.append(y_e)

# 合并所有场次
X_train = np.concatenate(train_Xs, axis=0).astype(np.float32)
y_train = np.concatenate(train_ys, axis=0).astype(np.float32)
X_test  = np.concatenate(test_Xs,  axis=0).astype(np.float32)
y_test  = np.concatenate(test_ys,  axis=0).astype(np.float32)

print(f"👉 训练样本: {X_train.shape[0]} 条，测试样本: {X_test.shape[0]} 条")

# === 9. 保存 ==================================================================
np.savez_compressed(out_dir/f"train_lb{look_back}.npz", X=X_train, y=y_train)
np.savez_compressed(out_dir/f"test_lb{look_back}.npz",  X=X_test,  y=y_test)
with open(out_dir/"minmax_scaler.pkl", "wb") as f:
    pickle.dump(scaler, f)

print("✔ 保存完成：")
print("  •", out_dir/f"train_lb{look_back}.npz")
print("  •", out_dir/f"test_lb{look_back}.npz")
print("  •", out_dir/"minmax_scaler.pkl")
