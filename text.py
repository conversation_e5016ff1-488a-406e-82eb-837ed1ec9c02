import paddle
import paddle.nn as nn
import paddle.nn.functional as F

# 1) 超参数
VOCAB_SIZE   = 10000      # 词表大小
EMBED_DIM    = 128        # 词向量维度
HIDDEN_SIZE  = 256        # LSTM 隐藏单元
NUM_LAYERS   = 2          # 堆叠层数
BIDIRECTION  = True       # 是否双向
DROPOUT      = 0.2        # LSTM 内部 dropout
NUM_CLASSES  = 3          # 要分类的类别数
BATCH_SIZE   = 32
SEQ_LEN      = 50
LR           = 1e-3
EPOCHS       = 5

# 2) 定义网络
class TextBiLSTM(nn.Layer):
    def __init__(self):
        super().__init__()
        self.embed = nn.Embedding(VOCAB_SIZE, EMBED_DIM)
        self.lstm  = nn.LSTM(
            input_size = EMBED_DIM,
            hidden_size = HIDDEN_SIZE,
            num_layers = NUM_LAYERS,
            direction  = 'bidirect' if BIDIRECTION else 'forward',
            dropout    = DROPOUT
        )  # API 说明见官方文档 :contentReference[oaicite:1]{index=1}
        self.fc = nn.Linear(HIDDEN_SIZE * (2 if BIDIRECTION else 1), NUM_CLASSES)

    def forward(self, x, seq_len=None):
        """
        x:  [batch, seq_len]  int64
        seq_len: [batch]      每个样本的有效长度（可选）
        """
        x = self.embed(x)                    # -> [batch, seq_len, embed_dim]
        out, (h_n, c_n) = self.lstm(x, sequence_length=seq_len)
        # 取最后一层、每个方向的最后一个时间步的隐藏状态
        final = paddle.concat([h_n[-2], h_n[-1]], axis=1) if BIDIRECTION else h_n[-1]
        logits = self.fc(final)              # -> [batch, num_classes]
        return logits

# 3) 构造伪数据（真实场景请换成 DataLoader）
def fake_batch(batch_size=BATCH_SIZE):
    x = paddle.randint(0, VOCAB_SIZE, shape=[batch_size, SEQ_LEN], dtype='int64')
    y = paddle.randint(0, NUM_CLASSES, shape=[batch_size], dtype='int64')
    return x, y

model = TextBiLSTM()
optimizer = paddle.optimizer.Adam(learning_rate=LR, parameters=model.parameters())
criterion = nn.CrossEntropyLoss()

# 4) 训练循环
for epoch in range(EPOCHS):
    model.train()
    for step in range(100):                      # 假设每个 epoch 100 个 batch
        x, y = fake_batch()
        logits = model(x)
        loss = criterion(logits, y)
        loss.backward()
        optimizer.step()
        optimizer.clear_grad()
        if step % 20 == 0:
            print(f"[epoch {epoch+1}] step {step}: loss = {loss.numpy()[0]:.4f}")
