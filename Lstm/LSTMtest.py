# LSTMtest.py  —— 完整可运行示例
# ==============================================================
import os
import numpy as np
import paddle
from paddle.io import Dataset, DataLoader
from tqdm import tqdm

# ---------- 路径 ----------
DATA_DIR  = (r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
             r"\预处理输出")
NPZ_PATH  = os.path.join(DATA_DIR, "test_look6.npz")
PARAMS    = os.path.join(DATA_DIR, "lstm_with_exog.pdparams")

# ---------- 数据集 ----------
class FloodSeqDataset(Dataset):
    def __init__(self, npz_path):
        z = np.load(npz_path)
        self.X_hist = z["X_hist"].astype("float32")   # (N, 6, F_in)
        self.X_exog = z["X_exog"].astype("float32")   # (N, 6)  ← 6 维
        self.y      = z["y"].astype("float32")

    def __len__(self):
        return self.y.shape[0]

    def __getitem__(self, idx):
        return self.X_hist[idx], self.X_exog[idx], self.y[idx]

test_ds  = FloodSeqDataset(NPZ_PATH)
test_loader = DataLoader(test_ds, batch_size=64, shuffle=False)

# ---------- 模型 ----------
from LSTM import LSTMWithExog    # 直接复用训练时的定义

INPUT_DIM = test_ds.X_hist.shape[-1]    # = 特征维度
EXOG_DIM  = test_ds.X_exog.shape[-1]    # = 6
model = LSTMWithExog(
    input_dim   = INPUT_DIM,
    hidden_size = 128,
    num_layers  = 2,
    exog_dim    = EXOG_DIM               # ★ 一定要吻合 6
)

# ---------- 加载参数 ----------
state_dict = paddle.load(PARAMS)
model.set_state_dict(state_dict)
model.eval()
print(f"✔ 已加载权重：{PARAMS}")

# ---------- 推理 ----------
preds, gts = [], []
with paddle.no_grad():
    for hist, exog, y in tqdm(test_loader, desc="Predict"):
        y_hat = model(hist, exog)
        preds.append(y_hat.numpy())
        gts.append(y.numpy())
preds = np.concatenate(preds, axis=0)
gts   = np.concatenate(gts,   axis=0)

# ---------- 简单评估 ----------
rmse = np.sqrt(np.mean((preds - gts) ** 2))
print(f"\nTest RMSE = {rmse:.6f}")
