# -*- coding: utf-8 -*-
"""
LSTMWithExog —— SHAP 解释脚本
-----------------------------------------------------------
★ 只需保证：
    1) 预处理好的  train_look6.npz / test_look6.npz
    2) 训练好的    lstm_with_exog.pdparams
    3) PaddlePaddle ≥2.5, SHAP, seaborn 已安装
其余 **模型代码、数据格式都不用动**。
"""
import os, pickle, sys, warnings
import numpy as np
import pandas as pd
import paddle
import paddle.nn as nn
import shap, seaborn as sns
import matplotlib.pyplot as plt
from tqdm import tqdm

# ============ ❶ 根据实际情况修改 ↓↓↓ =========================================
DATA_DIR       = (r"C:\Users\<USER>\Desktop\安徽中小流域资料整理 - 给孟涵 - 20230903"
                  r"\预处理输出")
TEST_NPZ       = os.path.join(DATA_DIR, "test_look6.npz")
MODEL_WEIGHTS  = os.path.join(DATA_DIR, "lstm_with_exog.pdparams")

LOOK_BACK      = 6          # 必须与训练保持一致
HIDDEN_SIZE    = 128
NUM_LAYERS     = 2
EXOG_DIM       = 2          # [下一小时总雨量, 下一小时蒸发]
# ❷ -------------------------------------------------------
# “单个小时原始变量”的顺序 = 训练/预处理时在 feature_df 中的列顺序
#       流量列 + 各雨量站 + 当前小时蒸发
BASE_VAR_NAMES = ["Q", "P1", "P2", "P3", "Evap"]
# ==============================================================================


# ---------------------- 数据加载 & 扁平化 -------------------------------------
npz = np.load(TEST_NPZ)
X_hist = npz["X_hist"].astype("float32")             # (N, 6, F_in)
X_exog = npz["X_exog"].astype("float32")             # (N, 2)
y_true = npz["y"]                                    # (N,)

N, LB, F_in = X_hist.shape
assert LB == LOOK_BACK, "LOOK_BACK 不匹配！"
assert F_in == len(BASE_VAR_NAMES), "BASE_VAR_NAMES 个数应等于 F_in"

# 折叠成一条长向量：hist + exog  -> (N, FLAT_DIM)
X_flat = np.concatenate([X_hist.reshape(N, -1), X_exog], axis=1)
FLAT_DIM = X_flat.shape[1]


# --------------------------- 复现模型结构 -------------------------------------
class LSTMWithExog(nn.Layer):
    def __init__(self, input_dim: int, hidden_size: int,
                 num_layers: int, exog_dim: int = 2):
        super().__init__()
        self.lstm = nn.LSTM(
            input_size=input_dim,
            hidden_size=hidden_size,
            num_layers=num_layers,
            direction="forward"
        )
        self.fc = nn.Sequential(
            nn.Linear(hidden_size + exog_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

    def forward(self, hist, exog):
        # hist:(B, LB, F) → (LB,B,F)
        hist = paddle.transpose(hist, perm=[1, 0, 2])
        out, _ = self.lstm(hist)
        last   = out[-1]                    # (B, hidden)
        x      = paddle.concat([last, exog], axis=1)
        y_hat  = self.fc(x)
        return y_hat.squeeze(1)             # (B,)


# --------------------------- 加载权重 -----------------------------------------
paddle.set_device("gpu" if paddle.is_compiled_with_cuda() else "cpu")
model = LSTMWithExog(F_in, HIDDEN_SIZE, NUM_LAYERS, EXOG_DIM)
state_dict = paddle.load(str(MODEL_WEIGHTS))   # str() 规避 Path 对象问题
model.set_state_dict(state_dict)
model.eval()
print(f"✔ 已加载权重：{MODEL_WEIGHTS}")


# -------------------- 构造特征名（决定解释粒度） -------------------------------
feature_names = []
for lag in range(LOOK_BACK, 0, -1):            # t-6 … t-1
    for v in BASE_VAR_NAMES:
        feature_names.append(f"{v}_t-{lag}")
# 下一小时外生量
feature_names += ["Rain+1h_total", "Evap+1h"]
assert len(feature_names) == FLAT_DIM, "feature_names 长度与输入维度不符！"


# ------------------------ 把 flat → (hist, exog) 的包装 ------------------------
def model_predict(flat_np: np.ndarray) -> np.ndarray:
    """SHAP 调用的预测函数；flat_np shape:(M, FLAT_DIM)"""
    flat_np = flat_np.astype("float32")
    M = flat_np.shape[0]
    hist_np = flat_np[:, :LOOK_BACK*F_in].reshape(M, LOOK_BACK, F_in)
    exog_np = flat_np[:, LOOK_BACK*F_in:]
    with paddle.no_grad():
        y_hat = model(paddle.to_tensor(hist_np),
                      paddle.to_tensor(exog_np)).numpy()
    return y_hat.flatten()                    # (M,)


# ------------------------- 计算 SHAP 值 (Kernel) ------------------------------
print("• 计算 SHAP 值…  (这一步可能需要几分钟)")
# ❸ 如数据量很大，可先随机抽样；这里用全部测试集
background   = shap.sample(X_flat, 100)       # 100 条背景
explainer    = shap.Explainer(model_predict, background)
shap_values  = explainer(X_flat, feature_names=feature_names)
# shap_values.values → (N, FLAT_DIM)

# --------------------- ① 变量×滞后 的平均|SHAP| 热图 --------------------------
print("• 生成平均贡献热图…")
shap_abs_mean = np.abs(shap_values.values).mean(axis=0)   # (FLAT_DIM,)
# 前 LOOK_BACK*F_in 维 = 历史序列
shap_mat = shap_abs_mean[:LOOK_BACK*F_in].reshape(LOOK_BACK, F_in)
lag_labels = [f"t-{i}" for i in range(LOOK_BACK, 0, -1)]
shap_df = pd.DataFrame(shap_mat, index=lag_labels, columns=BASE_VAR_NAMES)

plt.figure(figsize=(1.2*F_in, 0.7*LOOK_BACK+1))
sns.heatmap(shap_df, annot=True, fmt=".3f", cmap="Reds")
plt.title("Mean |SHAP|  (bigger ⇒ stronger impact)")
plt.tight_layout()
heatmap_png = os.path.join(DATA_DIR, "shap_heatmap.png")
plt.savefig(heatmap_png, dpi=300)
print(f"✔ 热图已保存：{heatmap_png}")

# --------------------- ② 单条样本 waterfall 示范 ------------------------------
idx = 0  # 改成你想看的样本序号
plt.figure(figsize=(6, 4))
shap.waterfall_plot(shap_values[idx], max_display=20, show=True)
plt.tight_layout()
wf_png = os.path.join(DATA_DIR, f"shap_waterfall_idx{idx}.png")
plt.savefig(wf_png, dpi=300)
print(f"✔ waterfall 图已保存：{wf_png}")
